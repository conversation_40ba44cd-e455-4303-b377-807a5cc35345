import requests

# Replace with your own Ethplorer API key
api_key = "**********************"

# Ethereum address to look up (replace with the partial address)
eth_address_start = "0x4838b"
eth_address_end = "BAD5f97"

# Base URL for the Ethplorer API
base_url = "https://api.ethplorer.io"

# List of potential hex characters to complete the address
hex_chars = "0123456789abcdef"

# Function to search for addresses based on partial match
def search_address(start, end, api_key):
    for mid in range(0, 2**16):
        mid_hex = f'{mid:04x}'
        potential_address = f'{start}{mid_hex}{end.lower()}'
        url = f"{base_url}/getAddressInfo/{potential_address}?apiKey={api_key}"
        response = requests.get(url)
        if response.status_code == 200 and "error" not in response.json():
            return potential_address
    return None

# Get the full address based on the partial match
eth_address = search_address(eth_address_start, eth_address_end, api_key)

if eth_address:
    ethplorer_url = f"https://ethplorer.io/address/{eth_address}"
    print(f"Address found: {ethplorer_url}")
else:
    print("Address not found")
