"""
CHECKPOINT: Working version of Binance Excel Report Analysis Tool
Date: 2025-06-18
Status: FULLY FUNCTIONAL

This module provides functionality to select and analyze Excel files
and generate a Word document summary report with customer data extraction.

Key Features:
- Analyzes Excel files and extracts sheet information
- Extracts customer data from 'Customer Information' sheets
- <PERSON>les vertical data layout (field names in one row, values in next row)
- Case-insensitive field matching
- Generates comprehensive Word document reports
- Fixed exception handling (no more broad Exception catching)

Successfully extracts:
- User ID, Email, Mobile, Registration time, Name, Status
- Handles single customer per file correctly
- Extracts actual data values (not just "X")
"""

import os
import tkinter as tk
from datetime import datetime
from tkinter import filedialog

import pandas as pd
from docx import Document


def select_excel_files():
    """
    Opens a file dialog for the user to select one or more Excel files.
    Returns a list of paths to the selected files.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main tkinter window
    file_paths = filedialog.askopenfilenames(
        title="Select Excel Files",
        filetypes=(("Excel files", "*.xlsx *.xls"), ("All files", "*.*")),
    )
    root.destroy()
    return file_paths


def extract_customer_data(file_path):
    """
    Extracts customer data from the 'CUSTOMER INFORMATION' sheet.
    Returns a list of customer records or None if sheet not found.
    """
    try:
        # First, get all sheet names to find the customer information sheet
        xls = pd.ExcelFile(file_path)
        sheet_names = xls.sheet_names

        # Look for customer information sheet with flexible matching
        customer_sheet_name = None
        possible_names = [
            "CUSTOMER INFORMATION",
            "Customer Information",
            "customer information",
            "CUSTOMER INFO",
            "Customer Info",
            "customer info",
            "CUSTOMERS",
            "Customers",
            "customers",
            "USER DATA",
            "User Data",
            "user data",
        ]

        for sheet_name in sheet_names:
            if sheet_name in possible_names:
                customer_sheet_name = sheet_name
                break

        if not customer_sheet_name:
            return None

        # Try to read the customer information sheet
        df = pd.read_excel(file_path, sheet_name=customer_sheet_name)

        # The data is organized vertically - field names in first column, values in subsequent columns
        # Create a single customer record by mapping field names to values
        customer = {
            "User ID": "X",
            "Email": "X",
            "Mobile": "X",
            "Registration time": "X",
            "Name": "X",
            "User nationality": "X",
            "Status": "X",
            "Residence": "X",
            "City": "X",
            "Address": "X",
            "Postal/Zip Code": "X",
        }

        # First, try to extract information from the header column name
        header_text = df.columns[0]
        if "id:" in header_text.lower():
            # Extract User ID from header
            import re

            id_match = re.search(r"id:\s*(\d+)", header_text)
            if id_match:
                customer["User ID"] = id_match.group(1)

        if "email:" in header_text.lower():
            # Extract Email from header
            email_match = re.search(r"email:\s*([^\s]+)", header_text)
            if email_match:
                customer["Email"] = email_match.group(1)

        if "time:" in header_text.lower():
            # Extract Registration time from header
            time_match = re.search(r"time:\s*([^)]+)", header_text)
            if time_match:
                customer["Registration time"] = time_match.group(1).strip()

        # Look through all rows to find field names and their corresponding values
        # The data appears to be in consecutive rows: field name in one row, value in the next
        rows_list = df.values.tolist()

        # Look for the header row and data row
        header_row_idx = None
        data_row_idx = None

        for i, row in enumerate(rows_list):
            non_null_values = [
                str(cell).strip()
                for cell in row
                if pd.notna(cell) and str(cell).strip()
            ]
            if non_null_values:
                # Look for a row that contains field names
                if any(
                    field.lower() in ["user id", "email", "mobile", "name"]
                    for field in non_null_values
                ):
                    header_row_idx = i
                    data_row_idx = i + 1
                    break

        if header_row_idx is not None and data_row_idx < len(rows_list):
            header_row = rows_list[header_row_idx]
            data_row = rows_list[data_row_idx]

            # Extract field-value pairs from corresponding positions
            for col_idx in range(min(len(header_row), len(data_row))):
                field_name = header_row[col_idx]
                field_value = data_row[col_idx]

                if pd.notna(field_name) and pd.notna(field_value):
                    field_name = str(field_name).strip()
                    field_value = str(field_value).strip()

                    if field_name and field_value:
                        # Map the field name to our standard fields (case-insensitive)
                        field_name_lower = field_name.lower().strip()

                        # More comprehensive field mappings with case-insensitive matching
                        if (
                            "user id" in field_name_lower
                            or "userid" in field_name_lower
                            or field_name_lower == "id"
                        ):
                            customer["User ID"] = field_value
                        elif "email" in field_name_lower:
                            customer["Email"] = field_value
                        elif (
                            "mobile" in field_name_lower or "phone" in field_name_lower
                        ):
                            customer["Mobile"] = field_value
                        elif (
                            "registration time" in field_name_lower
                            or "reg time" in field_name_lower
                            or "created" in field_name_lower
                        ):
                            customer["Registration time"] = field_value
                        elif (
                            "name" in field_name_lower
                            and "user" not in field_name_lower
                        ):
                            customer["Name"] = field_value
                        elif "nationality" in field_name_lower or (
                            "user" in field_name_lower
                            and "nationality" in field_name_lower
                        ):
                            customer["User nationality"] = field_value
                        elif "status" in field_name_lower:
                            customer["Status"] = field_value
                        elif "residence" in field_name_lower:
                            customer["Residence"] = field_value
                        elif "city" in field_name_lower:
                            customer["City"] = field_value
                        elif (
                            "address" in field_name_lower
                            and "email" not in field_name_lower
                        ):
                            customer["Address"] = field_value
                        elif "postal" in field_name_lower or "zip" in field_name_lower:
                            customer["Postal/Zip Code"] = field_value

        return [customer]  # Return as list with single customer

    except (
        ValueError,
        KeyError,
        pd.errors.ParserError,
        FileNotFoundError,
        PermissionError,
        UnicodeDecodeError,
        OSError,
    ):
        # Sheet not found or other Excel reading error
        return None


def analyze_excel_file(file_path):
    """
    Reads an Excel file, iterates through its sheets, and returns analysis
    data. Returns a dictionary with file analysis results.
    """
    file_result = {
        "file_path": file_path,
        "file_name": os.path.basename(file_path),
        "success": False,
        "error_message": None,
        "sheet_count": 0,
        "sheets": [],
        "customer_data": None,
    }

    try:
        xls = pd.ExcelFile(file_path)
        sheet_names = xls.sheet_names

        if not sheet_names:
            file_result["error_message"] = "No sheets found in this file."
            return file_result

        file_result["success"] = True
        file_result["sheet_count"] = len(sheet_names)

        for sheet_name in sheet_names:
            sheet_result = {"name": sheet_name}
            file_result["sheets"].append(sheet_result)

        # Extract customer data if CUSTOMER INFORMATION sheet exists
        customer_data = extract_customer_data(file_path)
        if customer_data:
            file_result["customer_data"] = customer_data

    except FileNotFoundError:
        file_result["error_message"] = "File not found"
    except (
        ValueError,
        PermissionError,
        pd.errors.ParserError,
        UnicodeDecodeError,
        OSError,
    ) as e:
        file_result["error_message"] = str(e)

    return file_result


def generate_word_report(analysis_results):
    """
    Generates a Word document with the import summary.
    """
    doc = Document()

    # Add title
    title = doc.add_heading("IMPORT SUMMARY", 0)
    title.alignment = 1  # Center alignment

    # Add timestamp
    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    doc.add_paragraph(f"Generated on: {timestamp}")
    doc.add_paragraph()

    # Calculate summary statistics
    total_files = len(analysis_results)
    successful_files = [r for r in analysis_results if r["success"]]
    failed_files = [r for r in analysis_results if not r["success"]]

    # Add summary section
    doc.add_heading("Summary Statistics", level=1)
    doc.add_paragraph(f"Total files processed: {total_files}")
    doc.add_paragraph(f"Files successfully read: {len(successful_files)}")
    doc.add_paragraph(f"Files that couldn't be read: {len(failed_files)}")
    doc.add_paragraph()

    # Add failed files section if any
    if failed_files:
        doc.add_heading("Files That Couldn't Be Read", level=1)
        for file_result in failed_files:
            doc.add_paragraph(f"• {file_result['file_name']}")
            error_msg = f"  Error: {file_result['error_message']}"
            doc.add_paragraph(error_msg, style="List Bullet 2")
        doc.add_paragraph()

    # Add successful files section
    if successful_files:
        doc.add_heading("Successfully Read Files", level=1)

        for file_result in successful_files:
            # File name as heading
            doc.add_heading(f"{file_result['file_name']}", level=2)
            sheet_count = file_result["sheet_count"]
            doc.add_paragraph(f"Number of sheets: {sheet_count}")

            # Sheet names
            doc.add_paragraph("Sheet Names:")
            for sheet in file_result["sheets"]:
                doc.add_paragraph(f"• {sheet['name']}", style="List Bullet")
            doc.add_paragraph()

            # Customer data section
            if file_result.get("customer_data"):
                doc.add_heading("CUSTOMER DATA", level=3)
                customers = file_result["customer_data"]

                # Since each file contains only 1 customer, we don't need to enumerate
                customer = customers[0]  # Get the single customer
                doc.add_paragraph("Customer Information:", style="Heading 4")

                # List all customer fields
                fields = [
                    "User ID",
                    "Email",
                    "Mobile",
                    "Registration time",
                    "Name",
                    "User nationality",
                    "Status",
                    "Residence",
                    "City",
                    "Address",
                    "Postal/Zip Code",
                ]

                for field in fields:
                    value = customer.get(field, "X")
                    field_text = f"• {field}: {value}"
                    doc.add_paragraph(field_text, style="List Bullet")
                doc.add_paragraph()  # Add space after customer info

    # Save the document
    timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Import_Summary_{timestamp_str}.docx"
    output_path = os.path.join(os.getcwd(), filename)
    doc.save(output_path)
    return output_path


def main():
    """
    Main function to select and analyze Excel files and generate Word report.
    """
    selected_files = select_excel_files()

    if not selected_files:
        print("No files selected.")
        return

    print("Analyzing files...")
    analysis_results = []

    for file_path in selected_files:
        print(f"Processing: {os.path.basename(file_path)}")
        result = analyze_excel_file(file_path)
        analysis_results.append(result)

    print("Generating Word document report...")
    output_path = generate_word_report(analysis_results)
    print(f"Report generated successfully: {output_path}")


if __name__ == "__main__":
    main()
