"""
This module provides functionality to select and analyze Excel files
and generate a Word document summary report.
"""

import os
import re
import tkinter as tk
from datetime import datetime
from tkinter import filedialog

import pandas as pd
from docx import Document


def select_excel_files():
    """
    Opens a file dialog for the user to select one or more Excel files.
    Returns a list of paths to the selected files.
    """
    root = tk.Tk()
    root.withdraw()  # Hide the main tkinter window
    file_paths = filedialog.askopenfilenames(
        title="Select Excel Files",
        filetypes=(("Excel files", "*.xlsx *.xls"), ("All files", "*.*")),
    )
    root.destroy()
    return file_paths


def _find_customer_sheet(sheet_names):
    """Find customer information sheet with flexible matching."""
    possible_names = [
        "CUSTOMER INFORMATION",
        "Customer Information",
        "customer information",
        "CUSTOMER INFO",
        "Customer Info",
        "customer info",
        "CUSTOMERS",
        "Customers",
        "customers",
        "USER DATA",
        "User Data",
        "user data",
    ]

    for sheet_name in sheet_names:
        if sheet_name in possible_names:
            return sheet_name
    return None


def _extract_from_header(header_text, customer):
    """Extract customer data from header text."""
    if "id:" in header_text.lower():
        id_match = re.search(r"id:\s*(\d+)", header_text)
        if id_match:
            customer["User ID"] = id_match.group(1)

    if "email:" in header_text.lower():
        email_match = re.search(r"email:\s*([^\s]+)", header_text)
        if email_match:
            customer["Email"] = email_match.group(1)

    if "time:" in header_text.lower():
        time_match = re.search(r"time:\s*([^)]+)", header_text)
        if time_match:
            customer["Registration time"] = time_match.group(1).strip()


def _find_data_rows(rows_list):
    """Find header and data rows in the Excel data."""
    for i, row in enumerate(rows_list):
        # Get non-null values from the row
        non_null_values = []
        for cell in row:
            if pd.notna(cell) and str(cell).strip():
                non_null_values.append(str(cell).strip())

        if non_null_values:
            # Look for a row that contains field names
            field_indicators = ["user id", "email", "mobile", "name"]
            has_field_indicator = any(
                field.lower() in field_indicators for field in non_null_values
            )
            if has_field_indicator:
                return i, i + 1
    return None, None


def _map_field_value(field_name_lower, field_value, customer):
    """Map field name to customer data field."""
    if (
        "user id" in field_name_lower
        or "userid" in field_name_lower
        or field_name_lower == "id"
    ):
        customer["User ID"] = field_value
    elif "email" in field_name_lower:
        customer["Email"] = field_value
    elif "mobile" in field_name_lower or "phone" in field_name_lower:
        customer["Mobile"] = field_value
    elif (
        "registration time" in field_name_lower
        or "reg time" in field_name_lower
        or "created" in field_name_lower
    ):
        customer["Registration time"] = field_value
    elif "name" in field_name_lower and "user" not in field_name_lower:
        customer["Name"] = field_value
    elif "nationality" in field_name_lower or (
        "user" in field_name_lower and "nationality" in field_name_lower
    ):
        customer["User nationality"] = field_value
    elif "status" in field_name_lower:
        customer["Status"] = field_value
    elif "residence" in field_name_lower:
        customer["Residence"] = field_value
    elif "city" in field_name_lower:
        customer["City"] = field_value
    elif "address" in field_name_lower and "email" not in field_name_lower:
        customer["Address"] = field_value
    elif "postal" in field_name_lower or "zip" in field_name_lower:
        customer["Postal/Zip Code"] = field_value


def _process_data_rows(header_row, data_row, customer):
    """Process header and data rows to extract customer information."""
    for col_idx in range(min(len(header_row), len(data_row))):
        field_name = header_row[col_idx]
        field_value = data_row[col_idx]

        if not (pd.notna(field_name) and pd.notna(field_value)):
            continue

        field_name = str(field_name).strip()
        field_value = str(field_value).strip()

        if field_name and field_value:
            field_name_lower = field_name.lower().strip()
            _map_field_value(field_name_lower, field_value, customer)


def extract_customer_data(file_path):
    """
    Extracts customer data from the 'CUSTOMER INFORMATION' sheet.
    Returns a list of customer records or None if sheet not found.
    """
    try:
        # First, get all sheet names to find the customer information sheet
        xls = pd.ExcelFile(file_path)
        sheet_names = xls.sheet_names

        # Look for customer information sheet with flexible matching
        customer_sheet_name = _find_customer_sheet(sheet_names)
        if not customer_sheet_name:
            return None

        # Try to read the customer information sheet
        df = pd.read_excel(file_path, sheet_name=customer_sheet_name)

        # Data is organized vertically - field names in first column,
        # values in subsequent columns
        # Create a single customer record by mapping field names to values
        customer = {
            "User ID": "X",
            "Email": "X",
            "Mobile": "X",
            "Registration time": "X",
            "Name": "X",
            "User nationality": "X",
            "Status": "X",
            "Residence": "X",
            "City": "X",
            "Address": "X",
            "Postal/Zip Code": "X",
        }

        # First, try to extract information from the header column name
        header_text = df.columns[0]
        _extract_from_header(header_text, customer)

        # Look through all rows to find field names and their
        # corresponding values
        # The data appears to be in consecutive rows: field name in one row,
        # value in the next
        rows_list = df.values.tolist()

        # Look for the header row and data row
        header_row_idx, data_row_idx = _find_data_rows(rows_list)

        if (
            header_row_idx is not None
            and data_row_idx is not None
            and data_row_idx < len(rows_list)
        ):
            header_row = rows_list[header_row_idx]
            data_row = rows_list[data_row_idx]
            _process_data_rows(header_row, data_row, customer)

        return [customer]  # Return as list with single customer

    except (
        ValueError,
        KeyError,
        pd.errors.ParserError,
        FileNotFoundError,
        PermissionError,
        UnicodeDecodeError,
        OSError,
    ):
        # Sheet not found or other Excel reading error
        return None


def analyze_excel_file(file_path):
    """
    Reads an Excel file, iterates through its sheets, and returns analysis
    data. Returns a dictionary with file analysis results.
    """
    file_result = {
        "file_path": file_path,
        "file_name": os.path.basename(file_path),
        "success": False,
        "error_message": None,
        "sheet_count": 0,
        "sheets": [],
        "customer_data": None,
    }

    try:
        xls = pd.ExcelFile(file_path)
        sheet_names = xls.sheet_names

        if not sheet_names:
            file_result["error_message"] = "No sheets found in this file."
            return file_result

        file_result["success"] = True
        file_result["sheet_count"] = len(sheet_names)

        for sheet_name in sheet_names:
            sheet_result = {"name": sheet_name}
            file_result["sheets"].append(sheet_result)

        # Extract customer data if CUSTOMER INFORMATION sheet exists
        customer_data = extract_customer_data(file_path)
        if customer_data:
            file_result["customer_data"] = customer_data

    except FileNotFoundError:
        file_result["error_message"] = "File not found"
    except (
        ValueError,
        PermissionError,
        pd.errors.ParserError,
        UnicodeDecodeError,
        OSError,
    ) as e:
        file_result["error_message"] = str(e)

    return file_result


def _add_report_header(doc):
    """Add title and timestamp to the report."""
    title = doc.add_heading("IMPORT SUMMARY", 0)
    title.alignment = 1  # Center alignment

    timestamp = datetime.now().strftime("%Y-%m-%d %H:%M:%S")
    doc.add_paragraph(f"Generated on: {timestamp}")
    doc.add_paragraph()


def _add_summary_statistics(doc, analysis_results):
    """Add summary statistics section to the report."""
    total_files = len(analysis_results)
    successful_files = [r for r in analysis_results if r["success"]]
    failed_files = [r for r in analysis_results if not r["success"]]

    doc.add_heading("Summary Statistics", level=1)
    doc.add_paragraph(f"Total files processed: {total_files}")
    doc.add_paragraph(f"Files successfully read: {len(successful_files)}")
    doc.add_paragraph(f"Files that couldn't be read: {len(failed_files)}")
    doc.add_paragraph()

    return successful_files, failed_files


def _add_failed_files_section(doc, failed_files):
    """Add failed files section to the report."""
    if not failed_files:
        return

    doc.add_heading("Files That Couldn't Be Read", level=1)
    for file_result in failed_files:
        doc.add_paragraph(f"• {file_result['file_name']}")
        error_msg = f"  Error: {file_result['error_message']}"
        doc.add_paragraph(error_msg, style="List Bullet 2")
    doc.add_paragraph()


def _add_customer_data_section(doc, file_result):
    """Add customer data section for a file."""
    if not file_result.get("customer_data"):
        print(f"No customer data found for {file_result['file_name']}")
        return

    doc.add_heading("CUSTOMER DATA", level=3)
    customers = file_result["customer_data"]

    # Since each file contains only 1 customer,
    # we don't need to enumerate
    customer = customers[0]  # Get the single customer
    doc.add_paragraph("Customer Information:", style="Heading 4")

    # List all customer fields
    fields = [
        "User ID",
        "Email",
        "Mobile",
        "Registration time",
        "Name",
        "User nationality",
        "Status",
        "Residence",
        "City",
        "Address",
        "Postal/Zip Code",
    ]

    for field in fields:
        value = customer.get(field, "X")
        field_text = f"• {field}: {value}"
        doc.add_paragraph(field_text, style="List Bullet")
    doc.add_paragraph()  # Add space after customer info


def _add_successful_files_section(doc, successful_files):
    """Add successful files section to the report."""
    if not successful_files:
        return

    doc.add_heading("Successfully Read Files", level=1)

    for file_result in successful_files:
        # File name as heading
        doc.add_heading(f"{file_result['file_name']}", level=2)
        doc.add_paragraph(f"Number of sheets: {file_result['sheet_count']}")

        # Sheet names
        doc.add_paragraph("Sheet Names:")
        for sheet in file_result["sheets"]:
            doc.add_paragraph(f"• {sheet['name']}", style="List Bullet")
        doc.add_paragraph()

        # Customer data section
        _add_customer_data_section(doc, file_result)


def generate_word_report(analysis_results):
    """
    Generates a Word document with the import summary.
    """
    doc = Document()

    _add_report_header(doc)
    successful_files, failed_files = _add_summary_statistics(doc, analysis_results)
    _add_failed_files_section(doc, failed_files)
    _add_successful_files_section(doc, successful_files)

    # Save the document
    timestamp_str = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"Import_Summary_{timestamp_str}.docx"
    output_path = os.path.join(os.getcwd(), filename)
    doc.save(output_path)
    return output_path


def main():
    """
    Main function to select and analyze Excel files and generate Word report.
    """
    selected_files = select_excel_files()

    if not selected_files:
        print("No files selected.")
        return

    print("Analyzing files...")
    analysis_results = []

    for file_path in selected_files:
        print(f"Processing: {os.path.basename(file_path)}")
        result = analyze_excel_file(file_path)
        analysis_results.append(result)

    print("Generating Word document report...")
    output_path = generate_word_report(analysis_results)
    print(f"Report generated successfully: {output_path}")


if __name__ == "__main__":
    main()
