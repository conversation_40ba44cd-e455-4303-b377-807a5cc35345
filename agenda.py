from openpyxl import Workbook
from openpyxl.styles import PatternFill
from datetime import datetime, timedelta
import holidays

# Configuratie
start_year = 2024
end_year = 2025
weekend_days = {5, 6}  # Zaterdag en zondag
teamleden_aantal = 20

# Kleuren voor weekend, feestdagen en CAT-categorieën
grey_fill_weekend = PatternFill(
    start_color="D3D3D3", end_color="D3D3D3", fill_type="solid"
)  # Voor weekends
grey_fill_holiday = PatternFill(
    start_color="A9A9A9", end_color="A9A9A9", fill_type="solid"
)  # Voor feestdagen
yellow_fill = PatternFill(
    start_color="F6FA70", end_color="F6FA70", fill_type="solid"
)  # rust
orange_fill = PatternFill(
    start_color="FFAF00", end_color="FFAF00", fill_type="solid"
)  # vakantie
blue_fill = PatternFill(
    start_color="0079FF", end_color="0079FF", fill_type="solid"
)  # telewerk
green_fill = PatternFill(
    start_color="16FF00", end_color="16FF00", fill_type="solid"
)  # opleiding
red_fill = PatternFill(
    start_color="FF0060", end_color="FF0060", fill_type="solid"
)  # ziek
purple_fill = PatternFill(
    start_color="A459D1", end_color="A459D1", fill_type="solid"
)  # andere

# Belgische en Vlaamse feestdagen ophalen
belgian_holidays = holidays.BE(years=range(start_year, end_year + 1))

# Dagnamen in het Nederlands
dagen_nl = {
    "Monday": "Ma",
    "Tuesday": "Di",
    "Wednesday": "Wo",
    "Thursday": "Do",
    "Friday": "Vr",
    "Saturday": "Za",
    "Sunday": "Zo",
}

# Maandnamen in het Nederlands
maanden_nl = {
    "Jan": "jan",
    "Feb": "feb",
    "Mar": "mrt",
    "Apr": "apr",
    "May": "mei",
    "Jun": "jun",
    "Jul": "jul",
    "Aug": "aug",
    "Sep": "sep",
    "Oct": "okt",
    "Nov": "nov",
    "Dec": "dec",
}

# Voeg "Andere" categorie toe aan de lijst van categorieën
categorieen = [
    "Vakantie",
    "Rust",
    "Telewerk",
    "Opleiding",
    "Ziek",
    "Andere",
]

# Kleuren voor de categorieën
category_colors = {
    "Vakantie": orange_fill,
    "Rust": yellow_fill,
    "Telewerk": blue_fill,
    "Opleiding": green_fill,
    "Ziek": red_fill,
    "Andere": purple_fill,
}

# Nieuwe Excel-werkboek aanmaken
wb = Workbook()

for year in range(start_year, end_year + 1):
    for month in range(1, 13):
        sheet_name = f"{year}-{month:02d}"
        ws = wb.create_sheet(title=sheet_name)

        # Headers instellen
        days_in_month = (
            (datetime(year, month + 1, 1) - timedelta(days=1)).day if month < 12 else 31
        )

        # First row for unique week numbers (only display on the first day of the week)
        week_numbers = []
        for day in range(1, days_in_month + 1):
            current_date = datetime(year, month, day)
            week_num = current_date.isocalendar()[1]
            # Only add the week number if it's a Monday or if it's the start of a new week
            if (
                current_date.weekday() == 0
                or current_date.day == 1
                or current_date.weekday() == 0
            ):
                week_numbers.append(f"Week {week_num}")
            else:
                week_numbers.append("")  # Leave blank for other days in the week

        # Add week numbers row at the top, starting from column C
        ws.append([""] * 2 + week_numbers)  # Empty first two columns for CAT and Naam

        # Headers for the "CAT" and "Naam" columns and dates
        header = ["CAT", "Naam"] + [
            f"{dagen_nl[datetime(year, month, day).strftime('%A')]} {day:02d}/{maanden_nl[datetime(year, month, day).strftime('%b')]}"
            for day in range(1, days_in_month + 1)
        ]
        ws.append(header)

        # Separate row for the holiday names, directly under the date row
        holiday_row = [""] * 2 + [
            belgian_holidays.get(datetime(year, month, day), "")
            for day in range(1, days_in_month + 1)
        ]
        ws.append(holiday_row)

        # Rijen voor teamleden (start vanaf rij 4, want rij 2 is voor feestdagen en rij 3 is voor de datum)
        for row in range(
            4, teamleden_aantal + 4
        ):  # Tot en met rij voor 20 leden, start bij rij 4
            # Vul in voor de eerste 7 leden, met verschillende categorieën
            if (
                row <= len(categorieen) + 3
            ):  # Vul in voor de eerste 7 leden, met verschillende categorieën
                ws.cell(row=row, column=1).value = categorieen[row - 4]
            else:
                ws.cell(row=row, column=1).value = ""  # Leeg voor de andere leden

            ws.cell(row=row, column=2).value = f"Lid {row-3}"  # Voorbeeldnaam

            # Kleur toepassen op de "CAT"-kolom
            category = ws.cell(row=row, column=1).value
            if category in category_colors:
                ws.cell(row=row, column=1).fill = category_colors[category]

        # Weekend of feestdag markeren en namen van feestdagen invullen
        for day in range(1, days_in_month + 1):
            current_date = datetime(year, month, day)
            column = day + 2  # Eerste kolom is "CAT", dagen starten vanaf kolom 3
            is_weekend = current_date.weekday() in weekend_days
            is_holiday = current_date in belgian_holidays

            # Markeer weekends
            if is_weekend:
                for row in range(4, teamleden_aantal + 4):  # Tot en met laatste lid
                    ws.cell(row=row, column=column).fill = grey_fill_weekend

            # Markeer feestdagen
            if is_holiday:
                for row in range(4, teamleden_aantal + 4):  # Tot en met laatste lid
                    ws.cell(row=row, column=column).fill = grey_fill_holiday

# Eerste lege sheet verwijderen
del wb["Sheet"]

# Freeze the first two columns
for sheet in wb.sheetnames:
    ws = wb[sheet]
    ws.freeze_panes = ws["C3"]

# Opslaan als Excel-bestand
output_path = r"C:/Users/<USER>/Documents/PYTHON/TeamAgenda_2024_2025.xlsx"
wb.save(output_path)

print(f"Agenda succesvol opgeslagen: {output_path}")
